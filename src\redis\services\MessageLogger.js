/**
 * MessageLogger - A service class for logging chat messages to Redis with media group support
 *
 * Handles the storage and management of chat message history in Redis, including:
 * - Storing message history with configurable retention
 * - Managing media groups for related media files
 * - Automatic cleanup of old messages
 * - Error handling for Redis operations
 *
 * @class
 * @param {Object} env - Environment variables including Redis configuration and bot settings
 */
import { getRedisClient } from '../redisClient.js';

export class MessageLogger {
	/**
	 * Creates an instance of MessageLogger
	 *
	 * @constructor
	 * @param {Object} env - Environment configuration containing:
	 *   - Redis connection details
	 *   - Bot configuration (TELEGRAM_BOT_USERNAME)
	 *   - Application settings
	 */
	constructor(env) {
		this.env = env;
		this.redis = getRedisClient(env);
		this.MAX_MESSAGES = 100; // Maximum number of messages to retain per chat
	}

	/**
	 * Logs a message to Redis with media group handling
	 *
	 * This method:
	 * 1. Constructs a Redis key for the chat's message history
	 * 2. Retrieves existing messages from Redis
	 * 3. Updates the message history with the new message
	 * 4. Truncates history if it exceeds MAX_MESSAGES
	 * 5. Stores the updated message history in Redis
	 * 6. Handles media groups by adding file info to media group lists
	 * 7. Logs operation results or errors
	 *
	 * All Redis operations are performed in a single pipeline transaction for atomicity.
	 *
	 * @async
	 * @param {string|number} chatId - Unique identifier for the chat
	 * @param {Object} messageData - Telegram message object to store
	 * @returns {void} Logs success or error to console, no return value
	 */
	async logMessage(chatId, messageData) {
		// Construct Redis key for this chat's message history
		const key = this._buildKey(chatId);

		try {
			// Retrieve existing messages from Redis
			const existingMessages = await this._getExistingMessages(key);

			// Update message history with new message (and truncate if needed)
			const updatedMessages = this._updateMessages(existingMessages, messageData);

			// Store updated messages and handle media groups in a single transaction
			await this._storeMessages(key, updatedMessages, messageData);

			// Log successful operation
			console.log(`Logged message to Redis for chat ${chatId}`);
		} catch (error) {
			// Log error but don't throw (graceful degradation)
			console.error(`Error logging message to Redis for chat ${chatId}: ${error.message}`);
		}
	}

	/**
	 * Constructs a Redis key for a chat's message history
	 *
	 * Key format: {bot_username}:chat:{chat_id}:messages
	 * This ensures keys are unique across different bots and chats
	 *
	 * @private
	 * @param {string|number} chatId - Unique identifier for the chat
	 * @returns {string} Redis key for storing message history
	 */
	_buildKey(chatId) {
		return `${this.env.TELEGRAM_BOT_USERNAME}:chat:${chatId.toString()}:messages`;
	}

	/**
	 * Retrieves existing messages from Redis for a chat
	 *
	 * Uses Redis GET command to retrieve the message history stored as a JSON string.
	 * Returns an empty array if no messages exist or if the key is not found.
	 *
	 * @async
	 * @private
	 * @param {string} key - Redis key for the chat's message history
	 * @returns {Array<Object>} Array of message objects, or empty array if none found
	 */
	async _getExistingMessages(key) {
		// Use Redis pipeline to get the message history
		const result = await this.redis.pipeline().get(key).exec();

		// Parse JSON string to array, or return empty array if no data
		try {
			return result[0] ? result[0] : [];
		} catch (error) {
			console.error(`Error parsing message history for key ${key}:`, error);
			return [];
		}
	}

	/**
	 * Updates message history with a new message and applies retention policy
	 *
	 * Adds the new message to the end of the history array and ensures the total
	 * number of messages does not exceed MAX_MESSAGES by removing oldest messages.
	 *
	 * @private
	 * @param {Array<Object>} existingMessages - Current message history
	 * @param {Object} messageData - New message to add to history
	 * @returns {Array<Object>} Updated message history with retention applied
	 */
	_updateMessages(existingMessages, messageData) {
		// Add new message to the end of the history
		existingMessages.push(messageData);

		// Apply retention policy: keep only the most recent MAX_MESSAGES
		return existingMessages.length > this.MAX_MESSAGES ? existingMessages.slice(-this.MAX_MESSAGES) : existingMessages;
	}

	/**
	 * Stores updated messages in Redis and handles media group associations
	 *
	 * Performs all Redis operations in a single atomic pipeline transaction:
	 * 1. Stores updated message history as JSON string
	 * 2. If message is part of a media group, adds file info to media group list
	 * 3. Sets expiration on media group (3600 seconds = 1 hour)
	 *
	 * Using a pipeline ensures atomicity - either all operations succeed or none do.
	 *
	 * @async
	 * @private
	 * @param {string} key - Redis key for message history
	 * @param {Array<Object>} messages - Updated message history array
	 * @param {Object} messageData - Original message data (for media group extraction)
	 */
	async _storeMessages(key, messages, messageData) {
		// Create Redis pipeline for atomic operations
		const pipeline = this.redis.pipeline();

		// Store updated message history as JSON string
		pipeline.set(key, JSON.stringify(messages));

		// Extract file information if message contains media
		const fileInfo = this._extractFileInfo(messageData);

		// If file info exists and message is part of a media group, update media group
		if (fileInfo) {
			this._addMediaGroupToPipeline(pipeline, messageData.media_group_id, fileInfo);
		}

		// Execute all operations as a single atomic transaction
		await pipeline.exec();
	}

	/**
	 * Extracts file information from a message for media group handling
	 *
	 * Identifies if a message contains media (photo or document) and extracts
	 * relevant information for media group processing. Only processes messages
	 * that are part of a media group (have media_group_id).
	 *
	 * Supported media types:
	 * - Photos: extracts file_id
	 * - Documents: extracts file_id and mime_type
	 *
	 * @private
	 * @param {Object} messageData - Telegram message object
	 * @returns {Object|null} File info object with type and identifiers, or null if no media
	 */
	_extractFileInfo(messageData) {
		// Only process messages that are part of a media group
		if (!messageData.media_group_id) return null;

		// Extract info for photos
		if (messageData.photo?.file_id) {
			return {
				type: 'photo',
				file_id: messageData.photo.file_id,
			};
		}

		// Extract info for documents
		if (messageData.document?.file_id) {
			return {
				type: 'document',
				file_id: messageData.document.file_id,
				mime_type: messageData.document.mime_type,
			};
		}

		// Return null for other message types or if no file_id found
		return null;
	}

	/**
	 * Adds media group operations to a Redis pipeline
	 *
	 * Adds two operations to the provided pipeline:
	 * 1. RPUSH: Appends file info to the media group list
	 * 2. EXPIRE: Sets 1-hour expiration on the media group key
	 *
	 * The 1-hour expiration ensures media group data is automatically cleaned up
	 * if not processed within a reasonable timeframe.
	 *
	 * @private
	 * @param {Object} pipeline - Redis pipeline instance
	 * @param {string} mediaGroupId - Unique identifier for the media group
	 * @param {Object} fileInfo - File information to add to the media group
	 */
	_addMediaGroupToPipeline(pipeline, mediaGroupId, fileInfo) {
		// Create Redis key for the media group
		const mediaGroupKey = `mediaGroup:${mediaGroupId}`;

		// Add file info to the end of the media group list
		pipeline.rpush(mediaGroupKey, JSON.stringify(fileInfo));

		// Set 1-hour expiration on the media group (3600 seconds)
		pipeline.expire(mediaGroupKey, 3600);

		// Log the operation
		console.log(`Appended file info ${JSON.stringify(fileInfo)} to media group ${mediaGroupId}`);
	}
}
