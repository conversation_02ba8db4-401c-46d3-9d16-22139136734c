/**
 * @fileoverview This file contains general application configuration constants.
 */

/**
 * A flag to enable or disable the response refinement feature.
 * @type {boolean}
 */
export const REFINE_RESPONSE = false;

/**
 * An array of allowed MIME types for file attachments.
 * @type {string[]}
 */
export const ALLOWED_FILE_TYPES = ['application/pdf', 'image/png', 'image/jpeg', 'text/plain'];

/**
 * Configuration settings for the fact extraction process, including
 * retry attempts, fact age, and quality score thresholds.
 * @type {{MAX_RETRY_ATTEMPTS: number, MAX_FACT_AGE_DAYS: number, MIN_FACT_QUALITY_SCORE: number}}
 */
export const FACT_EXTRACTION_CONFIG = {
	MAX_RETRY_ATTEMPTS: 3,
	MAX_FACT_AGE_DAYS: 30,
	MIN_FACT_QUALITY_SCORE: 0.7,
};