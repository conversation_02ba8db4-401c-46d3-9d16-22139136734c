import { getRedisClient } from '../redisClient.js';

/**
 * Manages chat history operations in Redis.
 * This class provides methods to delete chat history for specific conversations.
 */
export class ChatHistoryManager {
	/**
	 * Creates a new ChatHistoryManager instance.
	 * @param {Object} env - Environment variables containing configuration
	 * @param {string} env.TELEGRAM_BOT_USERNAME - The Telegram bot username used in key construction
	 */
	constructor(env) {
		this.env = env;
		this.redis = getRedisClient(env);
	}

	/**
	 * Deletes all chat history for a specific chat ID.
	 * @param {number|string} chatId - The unique identifier of the chat
	 * @returns {Promise<boolean>} True if deletion was successful or if no history existed, false on error
	 */
	async deleteChatHistory(chatId) {
		const key = this._buildKey(chatId);

		try {
			const exists = await this.redis.exists(key);
			if (!exists) {
				console.log(`[ChatHistoryManager] No chat history found for chat ${chatId}`);
				return true;
			}

			const result = await this.redis.del(key);
			const success = result > 0;

			console.log(`[ChatHistoryManager] ${success ? 'Successfully deleted' : 'Failed to delete'} chat history for chat ${chatId}`);
			return success || true; // Consider non-existent keys as successful deletion
		} catch (error) {
			console.error(`[ChatHistoryManager] Error deleting chat history for chat ${chatId}:`, error);
			return false;
		}
	}

	/**
	 * Builds the Redis key for storing chat history.
	 * The key format is: {bot_username}:chat:{chat_id}:messages
	 * @private
	 * @param {number|string} chatId - The unique identifier of the chat
	 * @returns {string} The constructed Redis key
	 */
	_buildKey(chatId) {
		return `${this.env.TELEGRAM_BOT_USERNAME}:chat:${chatId.toString()}:messages`;
	}
}
