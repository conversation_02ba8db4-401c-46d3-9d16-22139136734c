import { Index } from '@upstash/vector/cloudflare';

/**
 * Initializes and returns an Upstash Vector index instance.
 * @param {object} env - The Cloudflare environment variables.
 * @returns {Index} An instance of the Upstash Vector Index client.
 * @throws {Error} If the required Upstash environment variables are not set.
 */
export function getVectorStoreIndex(env) {
	const url = env.UPSTASH_VECTOR_REST_URL;
	const token = env.UPSTASH_VECTOR_REST_TOKEN;

	if (!url || !token) {
		throw new Error('Missing Upstash Vector environment variables: UPSTASH_VECTOR_REST_URL and/or UPSTASH_VECTOR_REST_TOKEN from env');
	}

	return new Index({
		url: url,
		token: token,
	});
}

/**
 * Checks if an error message indicates an API rate limit or quota exceedance.
 * This is used to gracefully handle API limits from Upstash Vector.
 * @param {Error} error - The error object to inspect.
 * @returns {boolean} - True if the error is determined to be a limit-related error, false otherwise.
 */
function isLimitError(error) {
	if (!error || !error.message) return false;

	const limitKeywords = ['rate limit', 'quota', 'max allowed read limit', 'too many requests', 'limit exceeded'];

	const errorMessage = error.message.toLowerCase();
	return limitKeywords.some((keyword) => errorMessage.includes(keyword));
}

/**
 * Deletes all vector embeddings associated with a specific chat ID.
 *
 * This function handles Upstash Vector's read and write limits by using a paginated approach.
 * It repeatedly queries for a batch of vectors matching the `chatId`, deletes them,
 * and continues until no more matching vectors are found.
 *
 * @param {object} env - The Cloudflare environment variables, containing UPSTASH_VECTOR_REST_URL and UPSTASH_VECTOR_REST_TOKEN.
 * @param {string} chatId - The unique identifier for the chat whose embeddings should be deleted.
 * @returns {Promise<boolean>} - Resolves to `true` if the deletion process completes (even if partially successful due to API limits),
 * and `false` if a critical, non-limit-related error occurs.
 */
export async function deleteChatEmbeddings(env, chatId) {
	// Max vectors to query per batch, staying safely under the Upstash 1000-item read limit.
	const BATCH_SIZE = 800;
	// A safety stop to prevent potential infinite loops during deletion.
	const MAX_BATCHES = 50;
	// Dimension of the vectors used for embeddings (e.g., Google's text-embedding-004).
	const VECTOR_DIMENSION = 768;

	try {
		const vectorIndex = getVectorStoreIndex(env);
		let batchCount = 0;
		let hasMoreResults = true;

		// To query by metadata filter alone, Upstash Vector requires a dummy vector.
		// A sparse vector is efficient for this purpose, as its content is irrelevant for a metadata-filtered query.
		const dummyVector = new Array(VECTOR_DIMENSION).fill(0);
		dummyVector[0] = 0.1; // A single non-zero value is sufficient for a valid query.

		let foundEmbeddings = false;

		try {
			const testResult = await vectorIndex.query({
				vector: dummyVector,
				topK: 10,
				includeMetadata: true,
				filter: `chatId = ${chatId}`,
			});

			if (testResult.length > 0) {
				foundEmbeddings = true;
			}
		} catch {
			// In case of a filter error, we can proceed, but the deletion loop might not find anything.
		}

		if (!foundEmbeddings) {
			return true; // No embeddings to delete is considered successful
		}

		while (hasMoreResults && batchCount < MAX_BATCHES) {
			batchCount++;

			try {
				// Query for a batch of vectors matching the chatId.
				const queryResult = await vectorIndex.query({
					vector: dummyVector,
					topK: BATCH_SIZE,
					includeMetadata: true,
					filter: `chatId = ${chatId}`,
				});

				// If the query returns no results, we are done.
				if (!queryResult || queryResult.length === 0) {
					hasMoreResults = false;
					break;
				}

				// Collect the unique IDs of the vectors found in this batch.
				const idsToDelete = queryResult.map((match) => match.id);

				// Delete the collected vectors by their IDs.
				if (idsToDelete.length > 0) {
					await vectorIndex.delete(idsToDelete);
				}

				// If the number of results is less than our batch size, it means we've processed the last page.
				if (queryResult.length < BATCH_SIZE) {
					hasMoreResults = false;
				}

				// A small delay between batches helps prevent hitting API rate limits.
				if (hasMoreResults) {
					await new Promise((resolve) => setTimeout(resolve, 100));
				}
			} catch (batchError) {
				// If we encounter a rate limit error, stop the process to avoid further issues.
				if (isLimitError(batchError)) {
					break;
				}

				// For other errors, we continue to the next batch.
			}
		}

		// Return true to indicate completion. Even a partial deletion due to rate limits is considered a successful run.
		return true;
	} catch {
		return false;
	}
}
