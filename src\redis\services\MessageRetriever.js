import { getRedisClient } from '../redisClient.js';

/**
 * Service class for retrieving chat message history from Redis
 * Handles fetching, filtering, and formatting previous conversation messages
 */
export class MessageRetriever {
	/**
	 * Initialize the MessageRetriever with environment configuration
	 * @param {Object} env - Environment configuration object
	 */
	constructor(env) {
		this.env = env;
		this.redis = getRedisClient(env);
		// Maximum number of messages to retrieve for chat history
		this.MESSAGE_HISTORY_LENGTH = 20;
	}

	/**
	 * Retrieve previous messages for a chat from Redis
	 * @param {string|number} chatId - Unique identifier for the chat
	 * @param {string} botUsername - Bot username (optional, uses env config if not provided)
	 * @returns {Array} Array of filtered message objects
	 */
	async getMessages(chatId, botUsername) {
		const key = this._buildKey(chatId, botUsername);

		try {
			const messages = await this.redis.get(key);
			return messages ? this._filterMessages(messages) : [];
		} catch (error) {
			console.error(`Error retrieving previous messages from Redis for chat ${chatId}: ${error.message}`);
			return [];
		}
	}

	/**
	 * Build Redis key for storing/retrieving chat messages
	 * Format: {username}:chat:{chatId}:messages
	 * @param {string|number} chatId - Unique identifier for the chat
	 * @param {string} botUsername - Bot username
	 * @returns {string} Redis key
	 */
	_buildKey(chatId, botUsername) {
		const username = botUsername || this.env.TELEGRAM_BOT_USERNAME;
		return `${username}:chat:${chatId.toString()}:messages`;
	}

	/**
	 * Filter and limit messages to only include valid text messages
	 * @param {Array} messages - Array of message objects
	 * @returns {Array} Filtered array of messages (text only, limited to MESSAGE_HISTORY_LENGTH)
	 */
	_filterMessages(messages) {
		return messages.filter((m) => m.text && m.text.trim() !== '').slice(-this.MESSAGE_HISTORY_LENGTH);
	}
}
