
import { getTelegramFile, arrayBufferToBase64 } from './telegramUtils.js';

/**
 * Downloads a file from Telegram and converts it to base64.
 * @param {string} botToken - The Telegram bot token.
 * @param {string} fileId - The file ID to download.
 * @returns {Promise<string>} - The base64 encoded file data.
 */
export async function downloadAndEncodeFile(botToken, fileId) {
	const fileUrl = await getTelegramFile(botToken, fileId);
	const response = await fetch(fileUrl);
	if (!response.ok) {
		throw new Error(`Failed to fetch file ${fileId}: ${response.statusText}`);
	}
	const fileArrayBuffer = await response.arrayBuffer();
	return arrayBufferToBase64(fileArrayBuffer);
}

/**
 * Helper function to fetch, process, and format a single file attachment.
 * @param {string} botToken - The Telegram bot token.
 * @param {string} fileId - The file ID to process.
 * @param {string} mimeType - The MIME type of the file.
 * @param {string[]} allowedFileTypes - An array of allowed MIME types.
 * @returns {Promise<object|null>} - The processed attachment part or null if unsupported.
 */
export async function fetchAndProcessFile(botToken, fileId, mimeType, allowedFileTypes) {
	if (!fileId) {
		return null;
	}
	if (!allowedFileTypes.includes(mimeType)) {
		console.log(`Skipping unsupported file type: ${mimeType}`);
		return null;
	}
	try {
		const base64FileData = await downloadAndEncodeFile(botToken, fileId);
		console.log(`Successfully processed file with file_id: ${fileId}`);
		return { inlineData: { mimeType: mimeType, data: base64FileData } };
	} catch (error) {
		console.error(`Error processing file with file_id ${fileId}:`, error);
		return null;
	}
}
