import { callFastGenerativeAI } from '../geminiAI.js';
import { COMPLEXITY_SCORING_PROMPT } from './constants.js';

/**
 * Analyzes the user's message to determine its complexity score.
 *
 * @param {object} env - The environment variables.
 * @param {string} messageText - The text of the user's message.
 * @returns {Promise<number>} A complexity score between 0.0 and 1.0.
 */
export async function getComplexityScore(env, messageText, redisHistory) {
	try {
		const config = {
			systemInstruction: COMPLEXITY_SCORING_PROMPT,
			temperature: 0.1, // Low temperature for deterministic scoring
		};
		const contents = [{ role: 'user', parts: [{ text: redisHistory + '\n' + `<USER_MESSAGE>${messageText}</USER_MESSAGE>` }] }];

		const response = await callFastGenerativeAI(env, config, contents);
		const score = parseFloat(response.text.trim());

		if (isNaN(score) || score < 0 || score > 1) {
			console.error(`Invalid complexity score received: ${response.text}`);
			return 0.5; // Return a default medium score on failure
		}

		return score;
	} catch (error) {
		console.error('Error getting complexity score:', error);
		return 0.5; // Return a default medium score on failure
	}
}
