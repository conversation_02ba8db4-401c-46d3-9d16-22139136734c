/**
 * @fileoverview API key management with rotation, error tracking, and distributed state for Gemini API.
 *
 * This implementation uses Redis to maintain key state across Cloudflare Workers instances,
 * providing persistent error tracking and intelligent key rotation.
 */
 
import { ApiKeyError } from '../errors/GeminiErrors.js';
import { getRedisClient } from '../../../redis/redisClient.js';

// Constants for key status management
const KEY_STATUS = {
  HEALTHY: 'healthy',
  COOLDOWN: 'cooldown',
  INVALID: 'invalid'
};

// Redis key prefixes
const REDIS_PREFIXES = {
  KEY_STATUS: 'gemini:api_key:status:',
  KEY_FAILURE_COUNT: 'gemini:api_key:failures:',
  KEY_LAST_ERROR: 'gemini:api_key:last_error:'
};

// Default cooldown durations in seconds
const DEFAULT_COOLDOWN = {
  RATE_LIMIT: 300,     // 5 minutes for rate limit errors (429)
  INVALID_KEY: 86400,  // 24 hours for invalid key errors (401)
  NETWORK_ERROR: 60    // 1 minute for network errors
};

/**
 * Manages API key rotation, error tracking, and distributed state for Gemini API
 */
export class ApiKeyManager {
	/**
	 * Initializes a new instance of the ApiKeyManager
	 * @param {object} env - Environment variables containing Redis configuration
	 */
	constructor(env) {
		this.env = env;
		this.redis = getRedisClient(env);
		this.geminiApiKeysCache = [];
		this.lastEnvKeysString = '';
	}

	/**
	 * Loads Gemini API keys from environment variables and ensures Redis state is initialized
	 * @param {object} env - Environment variables
	 * @returns {Promise<string[]>} Array of API keys
	 */
	async loadGeminiApiKeys(env) {
		// Check if environment variables related to keys have changed
		const currentEnvKeysString = JSON.stringify(
			Object.keys(env)
				.filter((key) => key.startsWith('GEMINI_API_KEY_'))
				.sort()
				.map((key) => env[key])
		);

		if (currentEnvKeysString === this.lastEnvKeysString && this.geminiApiKeysCache.length > 0) {
			return this.geminiApiKeysCache; // Return cached keys if no change
		}

		const keys = [];
		let i = 1;
		while (env[`GEMINI_API_KEY_${i}`]) {
			keys.push(env[`GEMINI_API_KEY_${i}`]);
			i++;
		}

		if (keys.length === 0 && env.GEMINI_API_KEY) {
			console.warn('No numbered Gemini API keys found. Falling back to single GEMINI_API_KEY from environment.');
			keys.push(env.GEMINI_API_KEY);
		}

		this.geminiApiKeysCache = keys;
		this.lastEnvKeysString = currentEnvKeysString;

		// Initialize Redis state for all keys
		await this._initializeKeyStates(keys);

		return keys;
	}

	/**
	 * Initializes Redis state for all API keys
	 * @param {string[]} keys - Array of API keys
	 * @private
	 */
	async _initializeKeyStates(keys) {
		if (!this.redis) return;

		try {
			// Use pipeline to set initial state for all keys
			const pipeline = this.redis.pipeline();
			
			keys.forEach(key => {
				const keyHash = this._getKeyHash(key);
				
				// Set initial status to healthy if not already set
				pipeline.setnx(`${REDIS_PREFIXES.KEY_STATUS}${keyHash}`, KEY_STATUS.HEALTHY);
				
				// Initialize failure count to 0
				pipeline.setnx(`${REDIS_PREFIXES.KEY_FAILURE_COUNT}${keyHash}`, 0);
				
				// Initialize last error to empty
				pipeline.setnx(`${REDIS_PREFIXES.KEY_LAST_ERROR}${keyHash}`, '');
			});
			
			await pipeline.exec();
		} catch (error) {
			console.warn('Failed to initialize key states in Redis:', error);
			// Continue execution even if Redis initialization fails
		}
	}

	/**
	 * Gets the next valid API key in rotation, considering key health and cooldown status
	 * @param {object} env - Environment variables
	 * @returns {Promise<string>} The selected API key
	 * @throws {ApiKeyError} If no valid API keys are available
	 */
	async getNextGeminiApiKey(env) {
		const apiKeys = await this.loadGeminiApiKeys(env);

		if (apiKeys.length === 0) {
			throw new ApiKeyError(
				'No Gemini API keys configured in environment variables (e.g., GEMINI_API_KEY_1, GEMINI_API_KEY_2, or GEMINI_API_KEY).'
			);
		}

		// Get healthy keys first
		const healthyKeys = await this._getHealthyKeys(apiKeys);
		
		if (healthyKeys.length > 0) {
			// Select from healthy keys using round-robin
			const selectedKey = this._selectFromKeys(healthyKeys);
			console.log(`Using healthy Gemini API key: ${selectedKey.substring(0, 10)}...`);
			return selectedKey;
		}

		// If no healthy keys, try keys coming out of cooldown
		const availableKeys = await this._getAvailableKeys(apiKeys);
		
		if (availableKeys.length > 0) {
			const selectedKey = this._selectFromKeys(availableKeys);
			console.log(`Using API key coming out of cooldown: ${selectedKey.substring(0, 10)}...`);
			return selectedKey;
		}

		// All keys are either invalid or in cooldown
		throw new ApiKeyError(
			'All Gemini API keys are currently unavailable due to errors or rate limiting.'
		);
	}

	/**
	 * Updates the status of an API key based on the error type
	 * @param {string} apiKey - The API key that failed
	 * @param {Error} error - The error that occurred
	 * @returns {Promise<void>}
	 */
	async updateKeyStatus(apiKey, error) {
		if (!this.redis) return;

		try {
			const keyHash = this._getKeyHash(apiKey);
			const errorStatus = this._getErrorStatus(error);
			
			if (errorStatus === KEY_STATUS.INVALID) {
				// Mark key as permanently invalid
				await this.redis.set(`${REDIS_PREFIXES.KEY_STATUS}${keyHash}`, KEY_STATUS.INVALID);
				await this.redis.set(`${REDIS_PREFIXES.KEY_LAST_ERROR}${keyHash}`, 'INVALID_KEY');
				console.log(`API key ${apiKey.substring(0, 10)}... marked as invalid due to authentication error`);
			} else if (errorStatus === KEY_STATUS.COOLDOWN) {
				// Increment failure count and set cooldown
				const failureCount = await this.redis.incr(`${REDIS_PREFIXES.KEY_FAILURE_COUNT}${keyHash}`);
				const cooldownSeconds = this._getCooldownDuration(error, failureCount);
				
				// Set status to cooldown with expiration
				await this.redis.setex(
					`${REDIS_PREFIXES.KEY_STATUS}${keyHash}`,
					cooldownSeconds,
					KEY_STATUS.COOLDOWN
				);
				
				// Store the error type
				await this.redis.set(`${REDIS_PREFIXES.KEY_LAST_ERROR}${keyHash}`, this._getErrorType(error));
				
				console.log(`API key ${apiKey.substring(0, 10)}... placed in ${cooldownSeconds}s cooldown due to ${this._getErrorType(error)}`);
			}
		} catch (redisError) {
			console.warn('Failed to update key status in Redis:', redisError);
		}
	}

	/**
	 * Gets healthy API keys that are not in cooldown
	 * @param {string[]} keys - Array of API keys to check
	 * @returns {Promise<string[]>} Array of healthy API keys
	 * @private
	 */
	async _getHealthyKeys(keys) {
		if (!this.redis) return keys; // Fallback to all keys if Redis is unavailable

		const healthyKeys = [];
		
		for (const key of keys) {
			const keyHash = this._getKeyHash(key);
			const status = await this.redis.get(`${REDIS_PREFIXES.KEY_STATUS}${keyHash}`);
			
			if (status === KEY_STATUS.HEALTHY) {
				healthyKeys.push(key);
			}
		}
		
		return healthyKeys;
	}

	/**
	 * Gets API keys that are available (healthy or coming out of cooldown)
	 * @param {string[]} keys - Array of API keys to check
	 * @returns {Promise<string[]>} Array of available API keys
	 * @private
	 */
	async _getAvailableKeys(keys) {
		if (!this.redis) return keys; // Fallback to all keys if Redis is unavailable

		const availableKeys = [];
		
		for (const key of keys) {
			const keyHash = this._getKeyHash(key);
			const status = await this.redis.get(`${REDIS_PREFIXES.KEY_STATUS}${keyHash}`);
			
			if (status === KEY_STATUS.HEALTHY || status === null) {
				availableKeys.push(key);
			}
		}
		
		return availableKeys;
	}

	/**
	 * Selects a key from an array using round-robin
	 * @param {string[]} keys - Array of keys to select from
	 * @returns {string} Selected key
	 * @private
	 */
	_selectFromKeys(keys) {
		// Use a simple counter stored in instance (will reset on worker restart)
		// In production, this could be stored in Redis for true distributed round-robin
		if (!this.rotationCounter) {
			this.rotationCounter = 0;
		}
		
		const selectedKey = keys[this.rotationCounter % keys.length];
		this.rotationCounter++;
		
		return selectedKey;
	}

	/**
	 * Determines the appropriate status based on error type
	 * @param {Error} error - The error that occurred
	 * @returns {string} KEY_STATUS value
	 * @private
	 */
	_getErrorStatus(error) {
		if (!error || !error.response) {
			return KEY_STATUS.COOLDOWN; // Network or unknown errors get cooldown
		}
		
		const statusCode = error.response.status;
		
		if (statusCode === 401 || statusCode === 403) {
			return KEY_STATUS.INVALID; // Authentication/authorization errors
		}
		
		if (statusCode === 429) {
			return KEY_STATUS.COOLDOWN; // Rate limiting
		}
		
		return KEY_STATUS.COOLDOWN; // All other errors get cooldown
	}

	/**
	 * Determines the cooldown duration based on error type and failure count
	 * @param {Error} error - The error that occurred
	 * @param {number} failureCount - Number of consecutive failures
	 * @returns {number} Cooldown duration in seconds
	 * @private
	 */
	_getCooldownDuration(error, failureCount) {
		if (!error || !error.response) {
			// Network errors - use exponential backoff
			return Math.min(DEFAULT_COOLDOWN.NETWORK_ERROR * Math.pow(2, failureCount - 1), 300); // Cap at 5 minutes
		}
		
		const statusCode = error.response.status;
		
		if (statusCode === 401 || statusCode === 403) {
			return DEFAULT_COOLDOWN.INVALID_KEY;
		}
		
		if (statusCode === 429) {
			// Rate limiting - use progressive cooldown based on failure count
			return Math.min(DEFAULT_COOLDOWN.RATE_LIMIT * Math.pow(1.5, failureCount - 1), 3600); // Cap at 1 hour
		}
		
		// Other errors - use network error cooldown with exponential backoff
		return Math.min(DEFAULT_COOLDOWN.NETWORK_ERROR * Math.pow(2, failureCount - 1), 300); // Cap at 5 minutes
	}

	/**
	 * Extracts the error type for logging
	 * @param {Error} error - The error that occurred
	 * @returns {string} Error type description
	 * @private
	 */
	_getErrorType(error) {
		if (!error || !error.response) {
			return 'NETWORK_ERROR';
		}
		
		const statusCode = error.response.status;
		
		switch (statusCode) {
			case 401:
			case 403:
				return 'AUTH_ERROR';
			case 429:
				return 'RATE_LIMIT';
			default:
				return `HTTP_${statusCode}`;
		}
	}

	/**
	 * Creates a hash of the API key for use as a Redis key
	 * @param {string} apiKey - The API key to hash
	 * @returns {string} Hashed key
	 * @private
	 */
	_getKeyHash(apiKey) {
		// Simple hash using first and last 8 characters
		// In production, consider using a proper hashing algorithm
		return `${apiKey.substring(0, 8)}_${apiKey.substring(apiKey.length - 8)}`;
	}

	/**
	 * Gets the current API key index for logging purposes
	 * @returns {number} Current API key index
	 */
	getCurrentApiKeyIndex() {
		return this.currentApiKeyIndex === 0 ? this.geminiApiKeysCache.length - 1 : this.currentApiKeyIndex - 1;
	}
}
