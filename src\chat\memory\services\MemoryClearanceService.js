import { RedisStorageService } from './RedisStorageService.js';

/**
 * Service class for clearing user memory data
 */
export class MemoryClearanceService {
	/**
	 * Creates an instance of MemoryClearanceService
	 * @param {Object} env - Environment configuration
	 * @param {RedisStorageService} [redisService] - Optional Redis service instance
	 */
	constructor(env, redisService = new RedisStorageService(env)) {
		this.redisService = redisService;
		this.actionableInsightsKeyPrefix = 'insights:';
	}

	/**
	 * Clears all memory data for a specific user
	 * @param {string} userId - The user ID to clear data for
	 * @returns {Promise<boolean>} - True if all deletions were successful, false otherwise
	 */
	async clear(userId) {
		try {
			console.log(`[MemoryClearanceService] Starting memory data clearing for user ${userId}`);

			let allSuccessful = true;

			// Delete actionable insights
			allSuccessful = await this._deleteKey(this.actionableInsightsKeyPrefix, userId, 'Insights', allSuccessful);

			console.log(`[MemoryClearanceService] Memory data clearing completed for user ${userId}. Success: ${allSuccessful}`);
			return allSuccessful;
		} catch (error) {
			this._handleError('CLEAR', userId, error);
			return false;
		}
	}

	/**
	 * Deletes a specific key for a user
	 * @param {string} keyPrefix - The key prefix to delete
	 * @param {string} userId - The user ID
	 * @param {string} logName - Name for logging purposes
	 * @param {boolean} allSuccessful - Current success status
	 * @returns {Promise<boolean>} Updated success status
	 */
	async _deleteKey(keyPrefix, userId, logName, allSuccessful) {
		try {
			const key = `${keyPrefix}${userId}`;
			console.log(`[MemoryClearanceService] Deleting ${logName.toLowerCase()} key: ${key}`);

			// Check if key exists first
			const exists = await this.redisService.exists(key);
			console.log(`[MemoryClearanceService] ${logName} key exists: ${exists}`);

			if (exists > 0) {
				const result = await this.redisService.del(key);
				console.log(`[MemoryClearanceService] ${logName} deletion result: ${result}`);

				if (result === 0) {
					console.error(`[MemoryClearanceService] Failed to delete ${logName.toLowerCase()} key: ${key}`);
					return false;
				}
			} else {
				console.log(`[MemoryClearanceService] No ${logName.toLowerCase()} found for user ${userId}`);
			}

			return allSuccessful;
		} catch (error) {
			this._handleError('DELETE', userId, error);
			return false;
		}
	}

	/**
	 * Handles errors
	 * @param {string} operation - Operation type
	 * @param {string} userId - User ID
	 * @param {Error} error - Error object
	 * @private
	 */
	_handleError(operation, userId, error) {
		console.error(`[MemoryClearanceService] Error during ${operation} operation for user ${userId}:`, error);
	}
}
