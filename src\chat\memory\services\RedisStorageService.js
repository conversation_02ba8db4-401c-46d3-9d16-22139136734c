import { getRedisClient } from '../../../redis.js';

/**
 * Service class for Redis storage operations
 */
export class RedisStorageService {
	/**
	 * Creates an instance of RedisStorageService
	 * @param {Object} env - Environment configuration
	 */
	constructor(env) {
		this.env = env;
		this.redis = getRedisClient(env);
	}

	/**
	 * Gets a value from Redis
	 * @param {string} key - Redis key
	 * @returns {Promise<string|null>} Value from Redis
	 */
	async get(key) {
		try {
			return await this.redis.get(key);
		} catch (error) {
			this._handleError('GET', key, error);
			return null;
		}
	}

	/**
	 * Sets a value in Redis
	 * @param {string} key - Redis key
	 * @param {string} value - Value to store
	 * @param {Object} [options] - Redis set options
	 * @returns {Promise<void>}
	 */
	async set(key, value, options = {}) {
		try {
			await this.redis.set(key, value, options);
		} catch (error) {
			this._handleError('SET', key, error);
		}
	}

	/**
	 * Deletes a key from Redis
	 * @param {string} key - Redis key
	 * @returns {Promise<number>} Number of keys deleted
	 */
	async del(key) {
		try {
			return await this.redis.del(key);
		} catch (error) {
			this._handleError('DEL', key, error);
			return 0;
		}
	}

	/**
	 * Checks if a key exists in Redis
	 * @param {string} key - Redis key
	 * @returns {Promise<number>} 1 if key exists, 0 otherwise
	 */
	async exists(key) {
		try {
			return await this.redis.exists(key);
		} catch (error) {
			this._handleError('EXISTS', key, error);
			return 0;
		}
	}

	/**
	 * Handles Redis errors
	 * @param {string} operation - Redis operation type
	 * @param {string} key - Redis key
	 * @param {Error} error - Error object
	 * @private
	 */
	_handleError(operation, key, error) {
		console.error(`[RedisStorageService] Error during ${operation} operation on key ${key}:`, error);
	}
}
