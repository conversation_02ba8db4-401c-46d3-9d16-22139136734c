/**
 * @fileoverview Factory for creating different types of AI models.
 */

import { ChatGoogleBase } from '@langchain/google-common';
import { GoogleGenerativeAIEmbeddings } from '@langchain/google-genai';
import { ChatOpenAI } from '@langchain/openai';
import { createReactAgent } from '@langchain/langgraph/prebuilt';
import { SerpAPI } from '@langchain/community/tools/serpapi';
import {
	DEFAULT_TEMPERATURE,
	DEFAULT_FAST_TEMPERATURE,
	DEFAULT_RESPONSE_MIME_TYPE,
	EMBEDDING_MODEL,
	DEFAULT_THINKING_BUDGET,
} from '../config/constants.js';

/**
 * Factory for creating different types of AI models
 */
export class ModelFactory {
	/**
	 * Creates a Gemini agent with LangGraph
	 * @param {string} apiKey - API key for authentication
	 * @param {string} modelName - Name of the model to use
	 * @param {object} config - AI configuration object
	 * @returns {object} LangGraph agent
	 */
	static createGeminiAgent(apiKey, modelName, config) {
		const model = new ChatGoogleBase({
			apiKey: apiKey,
			model: modelName,
			temperature: config.temperature || DEFAULT_TEMPERATURE,
			responseMimeType: config.responseMimeType || DEFAULT_RESPONSE_MIME_TYPE,
			thinkingBudget: config.thinkingBudget || DEFAULT_THINKING_BUDGET,
			platformType: 'gai',
			vertexai: false,
		});

		const serpTool = new SerpAPI(config.serpApiKey);

		return createReactAgent({
			llm: model,
			tools: config.tools || [serpTool],
			prompt: config.systemInstruction,
		});
	}

	/**
	 * Creates a fast AI model (Groq/Cerebras/OpenAI)
	 * @param {string} modelName - Name of the model to use
	 * @param {object} config - AI configuration object
	 * @returns {object} ChatOpenAI model instance
	 */
	static createFastModel(modelName, config) {
		return new ChatOpenAI({
			apiKey: config.apiKey,
			configuration: {
				baseURL: config.baseUrl,
			},
			model: modelName,
			temperature: config.temperature || DEFAULT_FAST_TEMPERATURE,
		});
	}

	/**
	 * Creates an embeddings instance
	 * @param {string} apiKey - API key for authentication
	 * @returns {GoogleGenerativeAIEmbeddings} Embeddings instance
	 */
	static createEmbeddingsInstance(apiKey) {
		return new GoogleGenerativeAIEmbeddings({
			apiKey: apiKey,
			model: EMBEDDING_MODEL,
			taskType: 'SEMANTIC_SIMILARITY',
		});
	}
}
