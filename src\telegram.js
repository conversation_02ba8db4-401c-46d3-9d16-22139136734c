import { USER_ERROR_MESSAGES } from './constants/index.js'; // Import the new error messages
import { escapeMdV2 } from './chat/telegramUtils.js'; // Import escapeMdV2

/**
 * Sends an error notification via Telegram to both the user and admin.
 * Handles user-friendly error messages based on error type and sends detailed
 * internal error reports to admin chat.
 *
 * @param {Object} env - Environment variables containing Telegram and system configuration
 * @param {Error} error - The error object to report
 * @param {Object} context - Additional context about the error (type, path, method, chatId, etc.)
 * @returns {Promise<void>}
 */
export const sendTelegramError = async (env, error, context = {}) => {
	// Extract required environment variables
	const botToken = env.TELEGRAM_BOT_TOKEN;
	const chatId = env.TELEGRAM_CHAT_ID;

	// Validate required configuration
	if (!botToken || !chatId) {
		console.error('Telegram Bot Token or Chat ID is missing in environment variables.');
		return;
	}

	// Determine appropriate user-friendly error message based on error type
	let userFriendlyMessage = USER_ERROR_MESSAGES.GENERIC_ERROR;
	if (context.type === 'rate_limit') {
		userFriendlyMessage = USER_ERROR_MESSAGES.RATE_LIMIT_ERROR;
	} else if (context.type === 'attachment_processing') {
		userFriendlyMessage = USER_ERROR_MESSAGES.ATTACHMENT_PROCESSING_ERROR;
	}
	// Add more conditions here for other specific error types if needed

	// Create timestamp for the error report using the configured timezone
	const timestamp = new Date().toLocaleString('en-US', { timeZone: env.TIMEZONE });

	// Format detailed internal error message for admin notification
	// Uses HTML parsing mode to preserve formatting, especially the <pre> tag for stack trace
	const internalErrorMessage = `🚨 Internal Error Alert 🚨

🕒 Time: ${timestamp} (${env.TIMEZONE})

📍 Path: ${context.path || 'N/A'}
🔗 Method: ${context.method || 'N/A'}
💬 Chat ID: ${context.chatId || 'N/A'}

💥 Message: ${error.message}

Stack:
<pre>${(error.stack || '').slice(0, 1000)}</pre>`; // Limit stack trace length for logs

	// URL for sending messages to users via the HRMNY bot
	const telegramBotUrl = `https://api.telegram.org/bot${env.HRMNY_BOT_TOKEN}/sendMessage`;

	try {
		// First, send user-friendly error message to the user's chat
		const response = await fetch(telegramBotUrl, {
			method: 'POST',
			headers: { 'Content-Type': 'application/json' },
			body: JSON.stringify({
				chat_id: context.chatId, // Target the user's chat
				text: escapeMdV2(userFriendlyMessage), // Escape special characters for MarkdownV2
				parse_mode: 'MarkdownV2', // Use MarkdownV2 for formatted user messages
			}),
		});

		// Handle response from user message attempt
		if (!response.ok) {
			const result = await response.json().catch(() => ({}));
			console.error(
				`Failed to send user-friendly Telegram error message: ${response.status} ${response.statusText} - ${
					result.description || 'No description'
				}`
			);
		} else {
			console.log(`User-friendly error message sent to chat ${context.chatId}`);
		}

		// URL for sending admin notifications using the main bot token
		const telegramUrl = `https://api.telegram.org/bot${botToken}/sendMessage`;

		// Send detailed internal error report to admin chat
		const adminResponse = await fetch(telegramUrl, {
			method: 'POST',
			headers: { 'Content-Type': 'application/json' },
			body: JSON.stringify({
				chat_id: chatId, // Send to the configured admin chat
				text: internalErrorMessage,
				parse_mode: 'HTML', // Use HTML parsing to preserve <pre> formatting for stack traces
			}),
		});

		// Handle response from admin message attempt
		if (!adminResponse.ok) {
			const adminResult = await adminResponse.json().catch(() => ({}));
			console.error(
				`Failed to send internal error report to admin chat: ${adminResponse.status} ${adminResponse.statusText} - ${
					adminResult.description || 'No description'
				}`
			);
		} else {
			console.log(`Internal error report sent to admin chat ${chatId}`);
		}
	} catch (err) {
		// Catch any errors that occur within the error reporting function itself
		console.error('Error in sendTelegramError function itself:', err);
	}
};

/**
 * Sends a 'typing' chat action to a Telegram chat.
 * This shows the typing indicator in the chat, improving user experience
 * when responses might take a moment to generate.
 *
 * @param {Object} env - Environment variables containing HRMNY_BOT_TOKEN
 * @param {string|number} chatId - The ID of the chat to send the typing action to
 * @returns {Promise<boolean>} - True if successful, false otherwise
 */
export const sendTypingAction = async (env, chatId) => {
	// Extract bot token from environment variables
	const botToken = env.HRMNY_BOT_TOKEN;

	// Validate required configuration
	if (!botToken) {
		console.error('Telegram Bot Token is missing in environment variables for typing action.');
		return false;
	}

	// Validate required parameter
	if (!chatId) {
		console.error('Chat ID is required to send a typing action.');
		return false;
	}

	// Construct the Telegram API URL for sendChatAction
	const telegramUrl = `https://api.telegram.org/bot${botToken}/sendChatAction`;

	// Prepare the request payload with chat ID and action type
	const actionData = {
		chat_id: chatId,
		action: 'typing', // Indicates that the bot is typing
	};

	try {
		// Send the request to Telegram API
		const response = await fetch(telegramUrl, {
			method: 'POST',
			headers: { 'Content-Type': 'application/json' },
			body: JSON.stringify(actionData),
		});

		// Handle HTTP errors
		if (!response.ok) {
			const result = await response.json().catch(() => ({}));
			console.error(`Failed to send typing action: ${response.status} ${response.statusText} - ${result.description || 'No description'}`);
			return false;
		}

		// Parse the response to check for API-level success
		const responseData = await response.json();

		// Check if Telegram API reported success
		if (responseData.ok) {
			console.log(`Typing action sent to chat ID: ${chatId}`);
			return true;
		} else {
			console.error(`Telegram API reported failure for typing action: ${responseData.description}`);
			return false;
		}
	} catch (error) {
		// Handle network errors or other exceptions
		console.error(`Error sending typing action to chat ID ${chatId}:`, error);
		return false;
	}
};

/**
 * Sends a message to a specific Telegram chat.
 * Handles message formatting and delivery with proper error handling.
 *
 * @param {Object} env - Environment variables containing HRMNY_BOT_TOKEN
 * @param {string|number} chatId - The ID of the chat to send the message to
 * @param {string} text - The message text content
 * @param {Object} options - Additional message options
 * @param {string} [options.parseMode] - Text parsing mode ('HTML', 'Markdown', 'MarkdownV2')
 * @param {number} [options.message_id] - If replying, the ID of the message being replied to
 * @returns {Promise<Object|null>} - Telegram API response on success, null on failure
 */
export const sendTelegramMessage = async (env, chatId, text, options = {}, suggestions = []) => {
	// Extract bot token from environment variables
	const botToken = env.HRMNY_BOT_TOKEN;

	// Validate required configuration
	if (!botToken) {
		console.error('Telegram Bot Token is missing in environment variables.');
		return null;
	}

	// Validate required parameter
	if (!chatId) {
		console.error('Chat ID is required to send a Telegram message.');
		return null;
	}

	// Construct the Telegram API URL for sendMessage
	const telegramUrl = `https://api.telegram.org/bot${botToken}/sendMessage`;

	// Prepare the message payload with required and optional parameters
	const messageData = {
		chat_id: chatId,
		text: text,
		parse_mode: 'MarkdownV2', // Default to MarkdownV2 for consistent formatting
		reply_parameters: options, // Use reply_parameters for modern Telegram API
	};

	if (suggestions.length > 0) {
		messageData.reply_markup = {
			keyboard: suggestions.map((suggestion) => [{ text: suggestion }]),
			resize_keyboard: true,
			one_time_keyboard: true,
		};
	}

	// Log the message data for debugging purposes
	console.log(JSON.stringify(messageData));

	try {
		// Send the message request to Telegram API
		const response = await fetch(telegramUrl, {
			method: 'POST',
			headers: { 'Content-Type': 'application/json' },
			body: JSON.stringify(messageData),
		});

		// Handle HTTP-level errors
		if (!response.ok) {
			const result = await response.json().catch(() => ({}));
			console.error(
				`Failed to send Telegram message: ${response.status} ${response.statusText} - ${result.description || 'No description'}`
			);
			return null;
		}

		// Parse and return the successful response
		const responseData = await response.json();
		console.log(`Telegram message sent to chat ID: ${chatId}`);
		return responseData;
	} catch (error) {
		// Handle network errors or other exceptions
		console.error(`Error sending Telegram message to chat ID ${chatId}:`, error);
		return null;
	}
};

// Maximum length for Telegram messages before they need to be split
// Telegram's message limit is approximately 4096 characters, but we use a more conservative
// limit to account for formatting and ensure reliability
export const MAX_MESSAGE_LENGTH = 1024;

/**
 * Splits a given text into chunks that do not exceed a specified maximum length,
 * attempting to split at natural word breaks where possible.
 * This function handles messages that exceed Telegram's length limits by intelligently
 * splitting text at logical boundaries.
 *
 * The algorithm prioritizes splitting by:
 * 1. Lines (preserving paragraph structure)
 * 2. Words (at spaces, avoiding mid-word breaks)
 * 3. Characters (as a last resort for very long words or strings without spaces)
 *
 * @param {string} text - The input text to split into chunks
 * @param {number} maxLength - The maximum character length for each chunk
 * @returns {string[]} An array of text chunks, each under the maxLength limit
 */
const splitTextIntoTelegramChunks = (text, maxLength) => {
	// Initialize array to store chunks and current chunk being built
	const chunks = [];
	// Split text into lines for processing
	const lines = text.split('\n');
	// Track the current chunk being constructed
	let currentChunk = '';

	// Process each line individually
	for (const line of lines) {
		// Handle lines that are longer than the maximum length individually
		if (line.length > maxLength) {
			// If we have a partially built chunk, save it first
			if (currentChunk.length > 0) {
				chunks.push(currentChunk);
				currentChunk = '';
			}

			// Process the long line by breaking it into smaller chunks
			let remainingLongLine = line;
			while (remainingLongLine.length > maxLength) {
				// Start with a split at the maximum length
				let splitPoint = maxLength;
				// Look at the portion that would be in the current chunk
				const tempPart = remainingLongLine.substring(0, maxLength);
				// Find the last space in this portion to split at a word boundary
				const lastSpaceIndex = tempPart.lastIndexOf(' ');

				// Only split at the space if it's not too close to the beginning
				// This prevents creating very small chunks at the start
				if (lastSpaceIndex > maxLength * 0.8) {
					splitPoint = lastSpaceIndex;
				}

				// Extract the chunk up to the split point and add to results
				chunks.push(remainingLongLine.substring(0, splitPoint));
				// Update the remaining portion, trimming any leading whitespace
				remainingLongLine = remainingLongLine.substring(splitPoint).trimStart();
			}

			// The remaining portion becomes the start of the next chunk
			currentChunk = remainingLongLine;
		} else {
			// Line is within the length limit
			// Create a potential new chunk by combining current chunk with this line
			const potentialNewChunk = currentChunk.length === 0 ? line : `${currentChunk}\n${line}`;

			if (potentialNewChunk.length > maxLength) {
				// Adding this line would exceed the limit
				// Save the current chunk and start a new one with this line
				chunks.push(currentChunk);
				currentChunk = line;
			} else {
				// Adding this line fits within the limit
				// Append it to the current chunk
				currentChunk = potentialNewChunk;
			}
		}
	}

	// Add any remaining content in the current chunk to the results
	if (currentChunk.length > 0) {
		chunks.push(currentChunk);
	}

	return chunks;
};

/**
 * Sends a potentially long message to a Telegram chat, automatically splitting it
 * into multiple messages if it exceeds the maximum length.
 * Handles reply threading appropriately by only including reply parameters
 * in the first message.
 *
 * @param {Object} env - Environment variables containing HRMNY_BOT_TOKEN
 * @param {string|number} chatId - The ID of the chat to send the message to
 * @param {string} text - The message text content, which may be long
 * @param {Object} options - Additional message options (e.g., reply parameters)
 * @returns {Promise<boolean>} - True if all message parts were sent successfully
 */
export const sendLongTelegramMessage = async (env, chatId, text, options = {}, suggestions = []) => {
	// If the message is within the length limit, send it directly
	if (text.length <= MAX_MESSAGE_LENGTH) {
		await sendTelegramMessage(env, chatId, text, options, suggestions);
		return true;
	}

	// Split the long message into appropriately sized chunks
	const chunks = splitTextIntoTelegramChunks(text, MAX_MESSAGE_LENGTH);

	// Track whether we're sending the first message (for reply handling)
	let firstMessage = true;

	// Send each chunk as a separate message
	for (let i = 0; i < chunks.length; i++) {
		const chunk = chunks[i];
		// Create a copy of options for this message
		const currentOptions = { ...options };

		// For subsequent messages, remove the reply parameter to avoid
		// replying to the same message multiple times, which could confuse users
		if (!firstMessage) {
			delete currentOptions.message_id;
		}

		// Only include suggestions on the last chunk
		const isLastChunk = i === chunks.length - 1;
		await sendTelegramMessage(env, chatId, chunk, currentOptions, isLastChunk ? suggestions : []);
		firstMessage = false;

		// Add a small delay between messages to prevent rate limiting
		await new Promise((resolve) => setTimeout(resolve, 500));
	}

	return true;
};
