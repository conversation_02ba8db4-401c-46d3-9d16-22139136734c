import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { secureHeaders } from 'hono/secure-headers';

// Import custom middleware for request processing.
import { validationMiddleware, errorHandlerMiddleware } from './middleware';

// Import application-specific routes.
import routes from './routes/index.js';

// Initialize the Hono application instance.
const app = new Hono();

/**
 * ==============================================================================
 * Global Middleware Registration
 * ==============================================================================
 * Middleware is registered globally and executes on every incoming request.
 * The order of registration is crucial as it defines the execution pipeline.
 */

// The error handler is registered first to catch any errors from subsequent middleware and routes.
app.use('*', errorHandlerMiddleware);

// Apply secure HTTP headers for enhanced security.
app.use('*', secureHeaders());

// Enable Cross-Origin Resource Sharing (CORS) for all routes.
// TODO: Restrict origin to the specific frontend URL in production for security.
app.use('*', cors(/* { origin: 'YOUR_FRONTEND_URL' } */));

// Custom middleware for validating incoming request data.
app.use('*', validationMiddleware);

/**
 * ==============================================================================
 * Route Registration
 * ==============================================================================
 * Mounts the application routes, making them accessible under the root path.
 */
app.route('/', routes);

/**
 * ==============================================================================
 * Cloudflare Worker Export
 * ==============================================================================
 * Exports the Hono app instance as the default handler for the Cloudflare Worker.
 */
export default app;
