/**
 * @fileoverview Manages chat context preparation for AI interactions.
 *
 * This module is responsible for assembling the complete context needed for AI responses
 * by combining various data sources including message history, semantic search results,
 * and user interactions. The context preparation process ensures that the AI has access
 * to relevant information while maintaining efficiency and avoiding duplication.
 *
 * The module orchestrates several key functions:
 * - Retrieval of previous messages from Redis storage
 * - Integration of semantic search results for long-term memory
 * - Handling of reply context to maintain conversation flow
 * - Formatting of messages for AI consumption
 * - Error handling to ensure graceful degradation
 *
 * @module chatContext
 */

// Import dependencies for context preparation
import { getPreviousMessages } from '../redis.js';                    // Retrieves message history from Redis
import { getHistory } from './search/semanticSearch.js';              // Performs semantic search for relevant context
import { formatRedisHistory, handleReplyContext } from './history/historyManager.js'; // Formats message history and handles reply context
import { formatCurrentMessage } from './utils/formatUtils.js';        // Formats the current message for AI processing
import { prepareFactContext } from './facts/factManager.js';          // Prepares user-specific facts for context
import { handle as handleAIResponse } from './response/aiResponseHandler.js'; // Handles AI response formatting and delivery

/**
 * Prepares the complete chat context for AI processing by combining various data sources.
 * This function orchestrates the assembly of context from multiple sources to provide
 * the AI model with comprehensive information for generating responses.
 *
 * The context preparation follows this flow:
 * 1. Retrieve previous messages from Redis storage
 * 2. Remove potential duplicates of the current message
 * 3. Augment with semantically relevant history from vector search
 * 4. Format the conversation history for AI consumption
 * 5. Add reply context if the message is a reply to another message
 * 6. Format the current message
 *
 * @param {Object} env - Environment variables containing configuration and secrets
 * @param {string|number} chatId - The Telegram chat ID to prepare context for
 * @param {string} botUsername - The username of the bot to filter messages
 * @param {string} currentMessageText - The text of the current user message
 * @param {number} messageDate - Unix timestamp of the current message
 * @param {Object} messageData - Complete message data from the webhook
 * @returns {Object} An object containing the prepared context components:
 * @returns {string} return.chatContent - The formatted current message
 * @returns {Array} return.previousMessages - Array of previous message objects
 * @returns {string} return.formattedHistory - Formatted conversation history
 * @returns {string} return.semanticHistory - Semantically retrieved relevant context
 *
 * @example
 * const context = await prepareChatContext(env, chatId, botUsername, "Hello", Date.now(), messageData);
 */
export async function prepareChatContext(env, chatId, botUsername, currentMessageText, messageDate, messageData) {
	try {
		// Retrieve the conversation history from Redis storage
		let previousMessages = await getPreviousMessages(env, chatId, botUsername);

		// Prevent duplication by removing the last message if it matches the current message timestamp
		// This can happen when the message is already stored in Redis before context preparation
		if (messageDate && previousMessages.length > 0 && previousMessages[previousMessages.length - 1].date === messageDate) {
			previousMessages.pop();
		}

		// Retrieve semantically relevant historical messages using vector search
		// Pass previousMessages to help filter out newer messages that might be in the vector store
		const semanticHistory = await getHistory(env, chatId, currentMessageText, previousMessages);

		// Format the Redis message history into a string representation for the AI
		let formattedHistory = formatRedisHistory(previousMessages, env);

		// Process reply context if the current message is replying to another message
		const replyContext = handleReplyContext(messageData);
		if (replyContext) {
			// Append reply context to the formatted history, handling empty history case
			formattedHistory = formattedHistory ? `${formattedHistory}\n${replyContext}` : replyContext;
		}

		// Format the current user message for AI processing
		const chatContent = formatCurrentMessage(currentMessageText);

		// Log the prepared chat content for debugging and monitoring
		console.log("Prepared Chat Content (User's current message):", chatContent);

		// Return all context components needed for AI processing
		return {
			chatContent,
			previousMessages,
			formattedHistory,
			semanticHistory,
		};
	} catch (error) {
		// Handle any errors that occur during context preparation
		console.error('Error in prepareChatContext:', error);
		
		// Return minimal fallback context to allow basic response generation
		// This ensures graceful degradation when context preparation fails
		return {
			chatContent: formatCurrentMessage(currentMessageText),
			previousMessages: [],
			formattedHistory: '',
			semanticHistory: '',
		};
	}
}

/**
 * Re-exports functions for backward compatibility.
 * These exports maintain API compatibility with existing code that depends on
 * these functions being available from this module.
 *
 * @deprecated These exports are maintained for backward compatibility only.
 * New code should import these functions directly from their source modules.
 */
export { prepareFactContext, handleAIResponse };
