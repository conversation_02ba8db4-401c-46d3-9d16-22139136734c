import { Hono } from 'hono';

const healthRoutes = new Hono();

/**
 * Builds a standardized health check response
 * @param {object} c - Hono context object containing environment variables
 * @returns {object} Health status information
 */
const buildHealthResponse = (c) => ({
	status: 'healthy',
	timestamp: new Date().toISOString(),
	environment: c.env.DEV_MODE === 'true' ? 'development' : 'production',
});

/**
 * Health Check Endpoint
 * Returns the current health status of the application
 */
healthRoutes.get('/health', (c) => {
	return c.json(buildHealthResponse(c));
});

export default healthRoutes;
