import {
  customProvider,
  extractReasoningMiddleware,
  wrapLanguageModel,
} from 'ai';
// import { createOpenAICompatible } from '@ai-sdk/openai-compatible';
import { xai } from '@ai-sdk/xai';
import { mistral } from '@ai-sdk/mistral';
import {
  artifactModel,
  chatModel,
  reasoningModel,
  titleModel,
} from './models.test';
import { isTestEnvironment } from '../constants';

export const myProvider = isTestEnvironment
  ? customProvider({
      languageModels: {
        'chat-model': chatModel,
        'chat-model-reasoning': reasoningModel,
        'title-model': titleModel,
        'artifact-model': artifactModel,
      },
    })
  : customProvider({
      languageModels: {
        'chat-model': mistral('mistral-medium-latest'),
        'chat-model-reasoning': wrapLanguageModel({
          model: xai('grok-3-mini-latest'),
          middleware: extractReasoningMiddleware({ tagName: 'think' }),
        }),
        'title-model': mistral('mistral-medium-latest'),
        'artifact-model': mistral('mistral-medium-latest'),
      },
      imageModels: {
        'small-model': xai.imageModel('grok-2-image'),
      },
    });
