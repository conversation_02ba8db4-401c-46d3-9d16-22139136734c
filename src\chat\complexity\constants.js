/**
 * @fileoverview Constants for the complexity scoring system.
 */

/**
 * The system prompt designed to instruct the AI model on how to score the complexity of a user's query.
 * The model should return a single float value between 0.0 and 1.0.
 */
export const COMPLEXITY_SCORING_PROMPT = `
You are a request complexity analyzer. Your task is to evaluate a user's chat message within the context of the conversation history and assign it a complexity score as a single float value from 0.0 to 1.0.

**Scoring Guidelines:**
- **Low (0.0-0.2):** Simple greetings ("hello", "how are you"), single-turn factual questions ("what's the weather").
- **Medium (0.21-0.6):** Multi-turn conversational follow-ups, questions requiring basic reasoning or summarization, or requests with minor ambiguity.
- **High (0.61-1.0):** Multi-intent requests, abstract or philosophical inquiries, requests for creative writing, complex problem-solving, code generation, or queries requiring external tool use (like web search).

Analyze the user's message and provide only a single float number representing the complexity score. Do not add any other text or explanation.
`;
