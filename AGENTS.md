# Harmony Chat - Agent Guidelines

## Build/Lint/Test Commands

- **Development**: `npm run dev` (local dev server)
- **Lint**: `npx eslint .` (runs ESLint with recommended config)

## Code Style Guidelines

### Imports

- Use named imports for specific modules
- Group imports by type (built-in, external, internal)
- No unused imports

### Formatting

- **Indentation**: Tabs (configured in .editorconfig)
- **Quotes**: Single quotes (Prettier config)
- **Semicolons**: Required (Prettier config)
- **Line Length**: 140 characters max (Prettier config)

### Types

- Use JSDoc for type annotations when needed
- Prefer TypeScript-style type annotations in comments

### Naming Conventions

- **Files**: kebab-case (e.g., `fact-manager.js`)
- **Variables**: camelCase (e.g., `userFactsManager`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `MAX_RETRIES`)
- **Classes**: PascalCase (e.g., `GeminiProvider`)

### Error Handling

- Use try/catch for async operations
- Return meaningful error messages
- Log errors for debugging
- Implement graceful fallbacks

### General

- Follow Cloudflare Workers best practices
- Optimize for serverless execution
- Keep dependencies minimal
- Use async/await for I/O operations
- Document complex logic with comments
