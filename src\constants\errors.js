/**
 * @fileoverview This file contains error-related constants, including user-facing
 * messages and internal error types.
 */

// --- User-Facing Error Messages ---

/**
 * A collection of user-facing error messages for the Harmony chatbot.
 * These are intended to be friendly and provide a consistent user experience
 * during error scenarios.
 * @type {Object<string, string>}
 */
export const USER_ERROR_MESSAGES = {
	DEFAULT: 'Waduh, ada yang aneh nih. Coba lagi nanti ya? (._.)',
	TIMEOUT: 'Waduh, kelamaan nih. Coba lagi nanti ya? (._.)',
	MAX_RETRIES: 'Waduh, udah coba berkali-kali tapi masih gagal. Coba lagi nanti ya? (._.)',
	NO_RESPONSE: 'Waduh, nggak ada respons nih. Coba lagi nanti ya? (._.)',
	ATTACHMENT_PROCESSING_ERROR: 'Mon kesulitan memproses lampiranmu. Mungkin coba format lain atau kirim ulang? (._.)',
};

// --- Fact Extraction Errors ---

/**
 * An enumeration of error types specific to the fact extraction process.
 * @type {Object<string, string>}
 */
export const FACT_EXTRACTION_ERRORS = {
	INVALID_INPUT: 'INVALID_INPUT',
	AI_SERVICE_UNAVAILABLE: 'AI_SERVICE_UNAVAILABLE',
	REDIS_CONNECTION_ERROR: 'REDIS_CONNECTION_ERROR',
	TIMEOUT_ERROR: 'TIMEOUT_ERROR',
	VALIDATION_ERROR: 'VALIDATION_ERROR',
	PROCESSING_ERROR: 'PROCESSING_ERROR',
};