export class ErrorHandler {
	constructor() {
		this._errorTypes = null;
	}

	async _getErrorTypes() {
		if (!this._errorTypes) {
			this._errorTypes = (await import('../../constants/index.js')).FACT_EXTRACTION_ERRORS;
		}
		return this._errorTypes;
	}

	async createEmptyInputError() {
		const errorTypes = await this._getErrorTypes();
		const errorType = errorTypes?.PROCESSING_ERROR || 'PROCESSING_ERROR';

		return {
			success: false,
			error: 'Input text is empty',
			errorType
		};
	}

	async handleError(error) {
		const errorTypes = await this._getErrorTypes();
		const errorType = this._categorizeError(error, errorTypes);

		console.error('[extractAndStoreFacts] Error:', {
			errorType,
			message: error.message
		});

		return {
			success: false,
			error: error.message,
			errorType
		};
	}

	_categorizeError(error, errorTypes) {
		const message = error.message || '';
		
		if (message.includes('timeout')) return errorTypes.TIMEOUT_ERROR;
		if (message.includes('Redis') || message.includes('redis')) return errorTypes.REDIS_CONNECTION_ERROR;
		if (message.includes('Gemini') || message.includes('AI')) return errorTypes.AI_SERVICE_UNAVAILABLE;
		
		return errorTypes.PROCESSING_ERROR;
	}
}