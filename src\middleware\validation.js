/**
 * Hono middleware to validate incoming requests
 */
export const validationMiddleware = async (c, next) => {
	if (['POST'].includes(c.req.method)) {
		const contentType = c.req.header('Content-Type');
		if (!contentType || !contentType.toLowerCase().includes('application/json')) {
			console.log(`Invalid Content-Type: ${contentType}`);
			return c.json(
				{
					error: true,
					details: 'Invalid request',
					status: 400,
				},
				400
			);
		}
	}

	// If all checks pass, proceed to the next middleware or handler
	await next();
};
