import { getRedisClient } from '../redis.js';

/**
 * Redis key prefix for storing conversation state data
 * @type {string}
 */
const CONVERSATION_STATE_KEY_PREFIX = 'conversation_state:';

/**
 * Redis key prefix for storing conversation history data
 * @type {string}
 */
const CONVERSATION_HISTORY_KEY_PREFIX = 'conversation_history:';

/**
 * Repository class for managing conversation data in Redis.
 * Handles storage and retrieval of conversation state, history, and activity tracking.
 */
export class RedisConversationRepo {
	/**
	 * Initialize the Redis conversation repository
	 * @param {Object} env - Environment variables containing Redis configuration
	 */
	constructor(env) {
		this.redis = getRedisClient(env);
	}

	/**
	 * Retrieve the current conversation state for a chat
	 * @param {string|number} chatId - Unique identifier for the chat
	 * @returns {Promise<string|null>} Serialized conversation state or null if not found
	 */
	async getCurrentState(chatId) {
		try {
			const key = `${CONVERSATION_STATE_KEY_PREFIX}${chatId}`;
			const state = await this.redis.get(key);
			return state ? state : null;
		} catch (error) {
			console.error(`[RedisConversationRepo] Error getting state for chat ${chatId}:`, error);
			return null;
		}
	}

	/**
	 * Update the conversation state for a chat with a 24-hour TTL
	 * @param {string|number} chatId - Unique identifier for the chat
	 * @param {Object} state - Conversation state to store
	 * @returns {Promise<boolean>} True if successful, false otherwise
	 */
	async updateConversationState(chatId, state) {
		try {
			const key = `${CONVERSATION_STATE_KEY_PREFIX}${chatId}`;
			// Store with 24-hour expiration (86400 seconds)
			await this.redis.set(key, JSON.stringify(state), { ex: 86400 });
			return true;
		} catch (error) {
			console.error(`[RedisConversationRepo] Error updating state for chat ${chatId}:`, error);
			return false;
		}
	}

	/**
	 * Get the last activity timestamp for a chat
	 * @param {string|number} chatId - Unique identifier for the chat
	 * @returns {Promise<string|null>} Serialized activity data or null if not found
	 */
	async getLastActivity(chatId) {
		try {
			const key = `${CONVERSATION_HISTORY_KEY_PREFIX}${chatId}:last_activity`;
			const activity = await this.redis.get(key);
			return activity ? activity : null;
		} catch (error) {
			console.error(`[RedisConversationRepo] Error getting last activity for chat ${chatId}:`, error);
			return null;
		}
	}

	/**
	 * Update the last activity timestamp for a chat with a 7-day TTL
	 * @param {string|number} chatId - Unique identifier for the chat
	 * @param {string} [messageType='user'] - Type of message that triggered the activity
	 * @returns {Promise<boolean>} True if successful, false otherwise
	 */
	async updateLastActivity(chatId, messageType = 'user') {
		try {
			const key = `${CONVERSATION_HISTORY_KEY_PREFIX}${chatId}:last_activity`;
			const activity = {
				timestamp: Date.now() / 1000, // Store as Unix timestamp in seconds
				messageType,
			};
			// Store with 7-day expiration (604800 seconds)
			await this.redis.set(key, JSON.stringify(activity), { ex: 604800 });
			return true;
		} catch (error) {
			console.error(`[RedisConversationRepo] Error updating last activity for chat ${chatId}:`, error);
			return false;
		}
	}

	/**
	 * Clear all conversation data for a chat (state and activity)
	 * @param {string|number} chatId - Unique identifier for the chat
	 * @returns {Promise<boolean>} True if successful, false otherwise
	 */
	async clearConversationData(chatId) {
		try {
			const stateKey = `${CONVERSATION_STATE_KEY_PREFIX}${chatId}`;
			const activityKey = `${CONVERSATION_HISTORY_KEY_PREFIX}${chatId}:last_activity`;

			// Delete both state and activity keys concurrently
			await Promise.all([this.redis.del(stateKey), this.redis.del(activityKey)]);

			return true;
		} catch (error) {
			console.error(`[RedisConversationRepo] Error clearing data for chat ${chatId}:`, error);
			return false;
		}
	}
}
