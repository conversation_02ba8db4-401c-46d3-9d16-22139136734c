import { getRedisClient } from '../redisClient.js';

/**
 * Service class for managing user facts in Redis
 * Handles deletion of user facts and their backups
 */
export class UserFactsManager {
	/**
	 * Initialize the UserFactsManager with environment configuration
	 * @param {Object} env - Environment configuration object
	 */
	constructor(env) {
		this.env = env;
		this.redis = getRedisClient(env);
	}

	/**
	 * Delete all user facts and their backups for a specific user
	 * @param {string|number} userId - Unique identifier for the user
	 * @returns {boolean} True if all facts were successfully deleted, false otherwise
	 */
	async deleteUserFacts(userId) {
		const factsKey = `user:${userId}:facts`;
		
		try {
			const [mainDeleted, backupsDeleted] = await Promise.all([
				this._deleteMainFacts(factsKey, userId),
				this._deleteBackupFacts(userId)
			]);

			const success = mainDeleted && backupsDeleted;
			console.log(`[UserFactsManager] ${success ? 'Successfully deleted' : 'Partially deleted'} user facts for user ${userId}`);
			return success;
		} catch (error) {
			console.error(`[UserFactsManager] Error deleting user facts for user ${userId}:`, error);
			return false;
		}
	}

	/**
	 * Delete main user facts from Redis
	 * @param {string} factsKey - Redis key for user facts
	 * @param {string|number} userId - Unique identifier for the user
	 * @returns {boolean} True if facts were successfully deleted or didn't exist, false on error
	 */
	async _deleteMainFacts(factsKey, userId) {
		const exists = await this.redis.exists(factsKey);
		if (!exists) {
			console.log(`[UserFactsManager] No user facts found for user ${userId}`);
			return true;
		}

		const result = await this.redis.del(factsKey);
		return result > 0;
	}

	/**
	 * Delete backup user facts from Redis
	 * @param {string|number} userId - Unique identifier for the user
	 * @returns {boolean} True if all backup facts were successfully deleted, false on error
	 */
	async _deleteBackupFacts(userId) {
		const pattern = `user:${userId}:facts:backup:*`;
		
		try {
			const backupKeys = await this._scanKeys(pattern);
			if (!backupKeys.length) {
				console.log(`[UserFactsManager] No backup keys found for user ${userId}`);
				return true;
			}

			const results = await Promise.all(backupKeys.map(key => this.redis.del(key)));
			const deleted = results.reduce((sum, res) => sum + res, 0);
			console.log(`[UserFactsManager] Deleted ${deleted} backup keys for user ${userId}`);
			return deleted === backupKeys.length;
		} catch (error) {
			console.error(`[UserFactsManager] Error deleting backup facts for user ${userId}:`, error);
			return false;
		}
	}

	/**
	 * Scan Redis for keys matching a pattern
	 * @param {string} pattern - Pattern to match keys against
	 * @returns {Array} Array of matching keys
	 */
	async _scanKeys(pattern) {
		let cursor = '0';
		let keys = [];
		
		do {
			const [newCursor, foundKeys] = await this.redis.scan(cursor, { match: pattern, count: 100 });
			cursor = newCursor;
			keys.push(...foundKeys);
		} while (cursor !== '0');
		
		return keys;
	}
}