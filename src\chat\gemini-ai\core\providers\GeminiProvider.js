/**
 * @fileoverview
 * Manages interactions with the Gemini API for text completions. This provider handles API key
 * rotation and fallback mechanisms across different Gemini models to ensure reliability and
 * maximize success rates for completion requests.
 */

import { ModelFactory } from '../../factories/ModelFactory.js';
import { MessageConverter } from '../../utils/MessageConverter.js';
import { ApiKeyError } from '../../errors/GeminiErrors.js';
import { DEFAULT_GEMINI_MODELS } from '../../config/constants.js';
import { langfuseManager } from '../../utils/LangfuseManager.js';

/**
 * Implements a provider for Google's Gemini models, with support for API key management
 * and model fallbacks.
 */
export class GeminiProvider {
	/**
	 * Initializes a new instance of the GeminiProvider.
	 * @param {object} apiKeyManager - An instance of ApiKeyManager to handle Gemini API keys.
	 */
	constructor(apiKeyManager) {
		this.apiKeyManager = apiKeyManager;
	}

	/**
	 * Attempts to generate a text completion by trying different models and API keys.
	 * It iterates through available API keys and specified models, making completion
	 * requests until one succeeds.
	 * @param {object} env - The environment variables, used to load API keys.
	 * @param {object} config - Configuration for the completion request, including an optional model.
	 * @param {Array<object>} contents - The content or message history for the completion.
	 * @param {ErrorAggregator} errorAggregator - An object to collect errors from failed attempts.
	 * @returns {Promise<object|null>} The completion result or null if all attempts fail.
	 * @throws {ApiKeyError} If no Gemini API keys are available.
	 */
	async attemptCompletion(env, config, contents, errorAggregator) {
		const langfuseHandler = langfuseManager.getHandler(env, config);

		// Determine the models to try, using the specified model or falling back to defaults.
		const modelsToTry = (config.model ? [config.model] : DEFAULT_GEMINI_MODELS.split(',')).map((m) => m.trim()).filter(Boolean);

		// Add required ApiKeys
		config.serpApiKey = env.SERPAPI_API_KEY;

		// Try with available API keys
		const apiKey = await this.apiKeyManager.getNextGeminiApiKey(env);
		if (!apiKey) {
			throw new ApiKeyError('No Gemini API key available.');
		}

		// Try each model with the selected API key.
		for (const modelName of modelsToTry) {
			try {
				// If a completion is successful, return it immediately.
				return await this._attemptModelCompletion(modelName, config, apiKey, contents, langfuseHandler);
			} catch (error) {
				const context = `gemini/${modelName}`;
				console.error(`Failed chat completion with ${context}:`, error);
				errorAggregator.addError(context, error);
				
				// Update key status based on error
				await this.apiKeyManager.updateKeyStatus(apiKey, error);
			}
		}

		// If we get here, the first key failed for all models
		// No need to try other keys since getNextGeminiApiKey already handles rotation
		return null;
	}

	/**
	 * Attempts a single chat completion with a specific model and API key.
	 * @param {string} modelName - The name of the Gemini model to use.
	 * @param {object} config - Configuration for the model.
	 * @param {string} apiKey - The API key for the request.
	 * @param {Array<object>} contents - The message content for the completion.
	 * @param {object} langfuseHandler - The handler for logging to Langfuse.
	 * @returns {Promise<object>} The result from the model.
	 * @private
	 */
	async _attemptModelCompletion(modelName, config, apiKey, contents, langfuseHandler) {
		console.log(`Attempting chat completion with model: ${modelName}`);

		const agent = ModelFactory.createGeminiAgent(apiKey, modelName, config);
		const messages = MessageConverter.convertToLangChainMessages(contents);

		const invokeOptions = {};
		if (langfuseHandler) {
			invokeOptions.callbacks = [langfuseHandler];
		}

		const result = await agent.invoke({ messages }, invokeOptions);

		// Extract the content from the last message in the response.
		const lastMessage = result.messages[result.messages.length - 1];
		const responseContent = lastMessage.content;

		console.log(`Successfully completed chat with model: ${modelName}`);

		return {
			text: responseContent,
			thoughts: '', // Placeholder for any thoughts returned by the model
		};
	}
}
